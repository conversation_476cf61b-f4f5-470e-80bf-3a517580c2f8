/**
 * 导出功能测试用例
 */

import { ExportFormat, ExportType } from '@/types/export';
import { 
  generateExportFilename, 
  validateExportParams, 
  getExportTypeDisplayName,
  formatFileSize 
} from '@/utils/export';

describe('导出工具函数测试', () => {
  describe('generateExportFilename', () => {
    it('应该生成正确的文件名', () => {
      const filename = generateExportFilename('school_statistics', '2024-03', 'xlsx');
      expect(filename).toMatch(/^学校统计_2024-03_\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}\.xlsx$/);
    });

    it('应该处理没有月份的情况', () => {
      const filename = generateExportFilename('teacher_ranking', undefined, 'csv');
      expect(filename).toMatch(/^教师排名_\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}\.csv$/);
    });

    it('应该处理未知类型', () => {
      const filename = generateExportFilename('unknown_type', '2024-03', 'xlsx');
      expect(filename).toMatch(/^导出数据_2024-03_\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}\.xlsx$/);
    });
  });

  describe('validateExportParams', () => {
    it('应该验证必要参数', () => {
      const validParams = {
        sso_school_code: 'SCHOOL001',
        month: '2024-03',
        export_format: ExportFormat.XLSX,
      };
      
      const result = validateExportParams(validParams);
      expect(result.valid).toBe(true);
    });

    it('应该检测缺失的学校代码', () => {
      const invalidParams = {
        month: '2024-03',
        export_format: ExportFormat.XLSX,
      };
      
      const result = validateExportParams(invalidParams);
      expect(result.valid).toBe(false);
      expect(result.message).toBe('学校代码不能为空');
    });

    it('应该检测缺失的月份', () => {
      const invalidParams = {
        sso_school_code: 'SCHOOL001',
        export_format: ExportFormat.XLSX,
      };
      
      const result = validateExportParams(invalidParams);
      expect(result.valid).toBe(false);
      expect(result.message).toBe('月份不能为空');
    });

    it('应该检测缺失的导出格式', () => {
      const invalidParams = {
        sso_school_code: 'SCHOOL001',
        month: '2024-03',
      };
      
      const result = validateExportParams(invalidParams);
      expect(result.valid).toBe(false);
      expect(result.message).toBe('导出格式不能为空');
    });
  });

  describe('getExportTypeDisplayName', () => {
    it('应该返回正确的显示名称', () => {
      expect(getExportTypeDisplayName('school_statistics')).toBe('学校统计数据');
      expect(getExportTypeDisplayName('teacher_statistics')).toBe('教师统计数据');
      expect(getExportTypeDisplayName('teacher_ranking')).toBe('教师排名数据');
      expect(getExportTypeDisplayName('questionnaire_responses')).toBe('问卷响应数据');
      expect(getExportTypeDisplayName('comprehensive_report')).toBe('综合报表');
      expect(getExportTypeDisplayName('incomplete_students')).toBe('未完成学生名单');
    });

    it('应该处理未知类型', () => {
      expect(getExportTypeDisplayName('unknown_type')).toBe('未知类型');
    });
  });

  describe('formatFileSize', () => {
    it('应该正确格式化文件大小', () => {
      expect(formatFileSize(0)).toBe('0 B');
      expect(formatFileSize(1024)).toBe('1 KB');
      expect(formatFileSize(1048576)).toBe('1 MB');
      expect(formatFileSize(1073741824)).toBe('1 GB');
      expect(formatFileSize(1536)).toBe('1.5 KB');
    });
  });
});

describe('导出类型枚举测试', () => {
  it('应该包含所有导出类型', () => {
    expect(ExportType.SCHOOL_STATISTICS).toBe('school_statistics');
    expect(ExportType.TEACHER_STATISTICS).toBe('teacher_statistics');
    expect(ExportType.TEACHER_RANKING).toBe('teacher_ranking');
    expect(ExportType.QUESTIONNAIRE_RESPONSES).toBe('questionnaire_responses');
    expect(ExportType.COMPREHENSIVE_REPORT).toBe('comprehensive_report');
    expect(ExportType.INCOMPLETE_STUDENTS).toBe('incomplete_students');
  });

  it('应该包含所有导出格式', () => {
    expect(ExportFormat.XLSX).toBe('xlsx');
    expect(ExportFormat.CSV).toBe('csv');
  });
});

describe('导出参数构建测试', () => {
  it('应该正确构建学校统计导出参数', () => {
    const baseParams = {
      sso_school_code: 'SCHOOL001',
      month: '2024-03',
      export_format: ExportFormat.XLSX,
    };

    const schoolParams = {
      ...baseParams,
      questionnaire_id: 1,
      include_trend: true,
      include_teacher_ranking: true,
      include_incomplete_students: true,
    };

    expect(schoolParams.sso_school_code).toBe('SCHOOL001');
    expect(schoolParams.month).toBe('2024-03');
    expect(schoolParams.export_format).toBe('xlsx');
    expect(schoolParams.questionnaire_id).toBe(1);
    expect(schoolParams.include_trend).toBe(true);
    expect(schoolParams.include_teacher_ranking).toBe(true);
    expect(schoolParams.include_incomplete_students).toBe(true);
  });

  it('应该正确构建教师排名导出参数', () => {
    const baseParams = {
      sso_school_code: 'SCHOOL001',
      month: '2024-03',
      export_format: ExportFormat.XLSX,
    };

    const rankingParams = {
      ...baseParams,
      subject: '数学',
      department: '初中部',
      sort_by: 'average_score',
      sort_order: 'DESC' as const,
      limit: 100,
    };

    expect(rankingParams.subject).toBe('数学');
    expect(rankingParams.department).toBe('初中部');
    expect(rankingParams.sort_by).toBe('average_score');
    expect(rankingParams.sort_order).toBe('DESC');
    expect(rankingParams.limit).toBe(100);
  });
});
