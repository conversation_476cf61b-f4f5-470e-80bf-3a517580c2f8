/**
 * 导出相关类型定义
 * @description 统计数据导出功能的类型定义
 */

/**
 * 导出格式枚举
 */
export enum ExportFormat {
  XLSX = 'xlsx',
  CSV = 'csv',
}

/**
 * 导出类型枚举
 */
export enum ExportType {
  SCHOOL_STATISTICS = 'school_statistics',
  TEACHER_STATISTICS = 'teacher_statistics',
  TEACHER_RANKING = 'teacher_ranking',
  QUESTIONNAIRE_RESPONSES = 'questionnaire_responses',
  COMPREHENSIVE_REPORT = 'comprehensive_report',
  INCOMPLETE_STUDENTS = 'incomplete_students',
}

/**
 * 学校统计导出参数
 */
export interface ISchoolStatisticsExportParams {
  /** SSO学校代码 */
  sso_school_code: string;
  /** 问卷ID，可选 */
  questionnaire_id?: number;
  /** 月份，格式：YYYY-MM */
  month: string;
  /** 开始月份，格式：YYYY-MM，可选 */
  start_month?: string;
  /** 结束月份，格式：YYYY-MM，可选 */
  end_month?: string;
  /** 导出格式 */
  export_format: ExportFormat;
  /** 是否包含趋势分析 */
  include_trend?: boolean;
  /** 是否包含教师排名 */
  include_teacher_ranking?: boolean;
  /** 是否包含未完成学生统计 */
  include_incomplete_students?: boolean;
}

/**
 * 教师统计导出参数
 */
export interface ITeacherStatisticsExportParams {
  /** SSO学校代码 */
  sso_school_code: string;
  /** SSO教师ID */
  sso_teacher_id: string;
  /** 月份，格式：YYYY-MM */
  month: string;
  /** 开始月份，格式：YYYY-MM，可选 */
  start_month?: string;
  /** 结束月份，格式：YYYY-MM，可选 */
  end_month?: string;
  /** 导出格式 */
  export_format: ExportFormat;
  /** 是否包含评分分布 */
  include_distribution?: boolean;
  /** 是否包含关键词分析 */
  include_keywords?: boolean;
  /** 是否包含趋势分析 */
  include_trend?: boolean;
}

/**
 * 教师排名导出参数
 */
export interface ITeacherRankingExportParams {
  /** SSO学校代码 */
  sso_school_code: string;
  /** 月份，格式：YYYY-MM */
  month: string;
  /** 学科，可选 */
  subject?: string;
  /** 部门，可选 */
  department?: string;
  /** 排序字段 */
  sort_by?: string;
  /** 排序方向 */
  sort_order?: 'ASC' | 'DESC';
  /** 限制数量 */
  limit?: number;
  /** 导出格式 */
  export_format: ExportFormat;
}

/**
 * 问卷响应导出参数
 */
export interface IQuestionnaireResponsesExportParams {
  /** SSO学校代码 */
  sso_school_code: string;
  /** 问卷ID */
  questionnaire_id: number;
  /** 月份，格式：YYYY-MM */
  month: string;
  /** 年级代码，可选 */
  grade_code?: string;
  /** 班级代码，可选 */
  class_code?: string;
  /** 是否已完成，可选 */
  is_completed?: boolean;
  /** 是否包含答案详情 */
  include_answers?: boolean;
  /** 导出格式 */
  export_format: ExportFormat;
}

/**
 * 综合报表导出参数
 */
export interface IComprehensiveReportExportParams {
  /** SSO学校代码 */
  sso_school_code: string;
  /** 月份，格式：YYYY-MM */
  month: string;
  /** 开始月份，格式：YYYY-MM，可选 */
  start_month?: string;
  /** 结束月份，格式：YYYY-MM，可选 */
  end_month?: string;
  /** 导出格式 */
  export_format: ExportFormat;
  /** 是否包含学校概览 */
  include_school_summary?: boolean;
  /** 是否包含教师排名 */
  include_teacher_ranking?: boolean;
  /** 是否包含年级分析 */
  include_grade_analysis?: boolean;
  /** 是否包含学科分析 */
  include_subject_analysis?: boolean;
  /** 是否包含趋势分析 */
  include_trend_analysis?: boolean;
  /** 是否包含完成情况分析 */
  include_completion_analysis?: boolean;
}

/**
 * 未完成学生导出参数
 */
export interface IIncompleteStudentsExportParams {
  /** SSO学校代码 */
  sso_school_code: string;
  /** 问卷ID */
  questionnaire_id: number;
  /** 月份，格式：YYYY-MM */
  month: string;
  /** 年级代码，可选 */
  grade_code?: string;
  /** 班级代码，可选 */
  class_code?: string;
  /** 是否包含联系信息 */
  include_contact_info?: boolean;
  /** 导出格式 */
  export_format: ExportFormat;
}

/**
 * 通用导出参数
 */
export interface IGeneralExportParams {
  /** 导出类型 */
  export_type: ExportType;
  /** SSO学校代码 */
  sso_school_code: string;
  /** 月份，格式：YYYY-MM */
  month: string;
  /** 导出格式 */
  export_format: ExportFormat;
  /** 其他参数 */
  [key: string]: any;
}

/**
 * 导出选项配置
 */
export interface IExportOptions {
  /** 导出类型 */
  type: ExportType;
  /** 导出格式 */
  format: ExportFormat;
  /** 是否包含趋势分析 */
  includeTrend?: boolean;
  /** 是否包含趋势分析（综合报表专用） */
  includeTrendAnalysis?: boolean;
  /** 是否包含教师排名 */
  includeTeacherRanking?: boolean;
  /** 是否包含未完成学生统计 */
  includeIncompleteStudents?: boolean;
  /** 是否包含评分分布 */
  includeDistribution?: boolean;
  /** 是否包含关键词分析 */
  includeKeywords?: boolean;
  /** 是否包含答案详情 */
  includeAnswers?: boolean;
  /** 是否包含联系信息 */
  includeContactInfo?: boolean;
  /** 是否包含学校概览 */
  includeSchoolSummary?: boolean;
  /** 是否包含年级分析 */
  includeGradeAnalysis?: boolean;
  /** 是否包含学科分析 */
  includeSubjectAnalysis?: boolean;
  /** 是否包含完成情况分析 */
  includeCompletionAnalysis?: boolean;
  /** 开始月份 */
  startMonth?: string;
  /** 结束月份 */
  endMonth?: string;
  /** 教师ID（教师统计专用） */
  teacherId?: string;
  /** 年级代码 */
  gradeCode?: string;
  /** 班级代码 */
  classCode?: string;
  /** 学科 */
  subject?: string;
  /** 部门 */
  department?: string;
  /** 排序字段 */
  sortBy?: string;
  /** 排序方向 */
  sortOrder?: 'ASC' | 'DESC';
  /** 限制数量 */
  limit?: number;
}

/**
 * 导出状态
 */
export enum ExportStatus {
  IDLE = 'idle',
  PREPARING = 'preparing',
  EXPORTING = 'exporting',
  SUCCESS = 'success',
  ERROR = 'error',
}

/**
 * 导出任务信息
 */
export interface IExportTask {
  /** 任务ID */
  id: string;
  /** 导出类型 */
  type: ExportType;
  /** 导出状态 */
  status: ExportStatus;
  /** 进度百分比 */
  progress?: number;
  /** 文件名 */
  filename?: string;
  /** 文件大小 */
  fileSize?: number;
  /** 错误信息 */
  error?: string;
  /** 创建时间 */
  createdAt: Date;
  /** 完成时间 */
  completedAt?: Date;
}
