import {
  exportSchoolStatistics,
  exportTeacherStatistics,
  exportTeacherRanking,
  exportQuestionnaireResponses,
  exportComprehensiveReport,
  exportIncompleteStudents,
} from '@/services';
import type { IExportOptions } from '@/types/export';
import { ExportFormat, ExportType } from '@/types/export';
import type { IStatisticsQuery } from '@/types/statistics';
import { getUserInfo } from '@/utils/auth';
import { handleExportResponse, validateExportParams } from '@/utils/export';
import { useModel } from '@umijs/max';
import { message } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import ExportModal from './components/ExportModal';
import FilterForm from './components/FilterForm';
import IncompleteStudents from './components/IncompleteStudents';
import SchoolOverview from './components/SchoolOverview';
import TeacherDetailModal from './components/TeacherDetailModal';
import TeacherRanking from './components/TeacherRanking';
import './index.less';

/**
 * 统计分析页面
 */
const Statistics: React.FC = () => {
  const {
    // 状态
    loading,
    modalLoading,
    schoolStatistics,
    teacherRanking,
    teacherTotal,
    teacherDetail,
    scoreDistribution,
    keywordData,
    filters,
    incompleteStudents,
    incompleteStudentsLoading,

    // 统计任务相关状态
    statisticsTaskStatus,
    cachedIncompleteStudents,
    taskLoading,
    isPolling,

    // 方法
    fetchSchoolStatistics,
    fetchTeacherRanking,
    fetchTeacherDetail,
    fetchIncompleteStudents,
    clearTeacherDetail,
    clearIncompleteStudents,

    // 统计任务相关方法
    triggerStatistics,
    fetchStatisticsTaskStatus,
    stopPolling,
  } = useModel('statistics');

  // 教师详情模态框状态
  const [modalVisible, setModalVisible] = useState(false);

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  // 未填写学生显示状态
  const [showIncompleteStudents, setShowIncompleteStudents] = useState(false);

  // 未填写学生分页状态
  // const [incompleteStudentsPage, setIncompleteStudentsPage] = useState(1);
  const [incompleteStudentsPageSize, setIncompleteStudentsPageSize] =
    useState(20);
  const [incompleteStudentsFilters, setIncompleteStudentsFilters] =
    useState<any>({});

  // 导出相关状态
  const [exportModalVisible, setExportModalVisible] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);

  // 获取当前用户学校信息
  const getCurrentUserSchoolCode = () => {
    const userInfo = getUserInfo();
    return userInfo?.enterprise?.code || '';
  };

  // 初始化数据
  useEffect(() => {
    const initData = async () => {
      const schoolCode = getCurrentUserSchoolCode();
      const currentMonth = dayjs().format('YYYY-MM'); // 获取当前月份
      const defaultQuestionnaireId = 1; // 默认问卷ID，实际应该从问卷列表中获取

      // 先获取统计任务状态
      await fetchStatisticsTaskStatus(defaultQuestionnaireId);

      // 学校统计参数（不需要分页）
      const schoolParams: IStatisticsQuery = {
        sso_school_code: schoolCode,
        month: currentMonth,
      };

      // 教师排名参数（需要分页）
      const teacherParams: IStatisticsQuery = {
        sso_school_code: schoolCode,
        month: currentMonth,
        page: 1,
        limit: pageSize,
      };

      // 获取初始统计数据
      await Promise.all([
        fetchSchoolStatistics(schoolParams),
        fetchTeacherRanking(teacherParams),
      ]);
    };

    initData();
  }, [
    fetchSchoolStatistics,
    fetchTeacherRanking,
    fetchStatisticsTaskStatus,
    pageSize,
  ]);

  // 处理筛选
  const handleFilter = async (filterParams: IStatisticsQuery) => {
    const schoolCode = getCurrentUserSchoolCode();
    const questionnaireId = filterParams.questionnaire_id || 1;

    // 先获取统计任务状态
    await fetchStatisticsTaskStatus(questionnaireId);

    // 学校统计参数（不需要分页）
    const schoolParams = {
      ...filterParams,
      sso_school_code: schoolCode,
    };

    // 教师排名参数（需要分页）
    const teacherParams = {
      ...filterParams,
      sso_school_code: schoolCode,
      page: 1, // 重置到第一页
      limit: pageSize,
    };

    setCurrentPage(1);

    // 分别更新学校统计和教师排名
    await Promise.all([
      fetchSchoolStatistics(schoolParams),
      fetchTeacherRanking(teacherParams),
    ]);

    // 清空未填写学生数据
    clearIncompleteStudents();
  };

  // 处理重置筛选
  const handleReset = async () => {
    const schoolCode = getCurrentUserSchoolCode();
    const currentMonth = dayjs().format('YYYY-MM'); // 重置时也使用当前月份

    // 学校统计参数（不需要分页）
    const schoolParams: IStatisticsQuery = {
      sso_school_code: schoolCode,
      month: currentMonth,
    };

    // 教师排名参数（需要分页）
    const teacherParams: IStatisticsQuery = {
      sso_school_code: schoolCode,
      month: currentMonth,
      page: 1,
      limit: pageSize,
    };

    setCurrentPage(1);

    // 分别重置学校统计和教师排名
    await Promise.all([
      fetchSchoolStatistics(schoolParams),
      fetchTeacherRanking(teacherParams),
    ]);

    // 清空未填写学生数据
    clearIncompleteStudents();
    setShowIncompleteStudents(false);
  };

  // 处理教师详情点击
  const handleTeacherClick = async (teacherId: string) => {
    setModalVisible(true);
    await fetchTeacherDetail(teacherId, filters);
  };

  // 关闭教师详情模态框
  const handleModalClose = () => {
    setModalVisible(false);
    clearTeacherDetail();
  };

  // 处理分页变化
  const handlePageChange = async (page: number, size: number) => {
    const schoolCode = getCurrentUserSchoolCode();
    const params = {
      ...filters,
      sso_school_code: schoolCode,
      page,
      limit: size,
    };
    setCurrentPage(page);
    setPageSize(size);
    await fetchTeacherRanking(params);
  };

  // 处理排序变化
  const handleSortChange = async (
    sortBy: string,
    sortOrder: 'ASC' | 'DESC',
  ) => {
    const schoolCode = getCurrentUserSchoolCode();
    const params = {
      ...filters,
      sso_school_code: schoolCode,
      page: currentPage,
      limit: pageSize,
      sort_by: sortBy as any,
      sort_order: sortOrder,
    };
    await fetchTeacherRanking(params);
  };

  // 获取未填写学生数据
  const handleFetchIncompleteStudents = async (
    page: number = 1,
    pageSize: number = 20,
    additionalFilters: any = {},
  ) => {
    console.log('handleFetchIncompleteStudents called with:', {
      page,
      pageSize,
      additionalFilters,
    });

    // 确保参数是正确的类型
    const safePage = typeof page === 'number' ? page : 1;
    const safePageSize = typeof pageSize === 'number' ? pageSize : 20;

    const schoolCode = getCurrentUserSchoolCode();
    const currentMonth = dayjs().format('YYYY-MM');
    const params = {
      sso_school_code: schoolCode,
      month: filters.month || currentMonth,
      page: safePage,
      pageSize: safePageSize,
      ...incompleteStudentsFilters,
      ...additionalFilters,
    };
    console.log('handleFetchIncompleteStudents params:', params);
    await fetchIncompleteStudents(params);
    setShowIncompleteStudents(true);
  };

  // 刷新未填写学生数据
  const handleRefreshIncompleteStudents = async () => {
    if (showIncompleteStudents) {
      await handleFetchIncompleteStudents();
    }
  };

  // 处理未填写学生分页变化
  const handleIncompleteStudentsPageChange = async (
    page: number,
    pageSize: number,
  ) => {
    // setIncompleteStudentsPage(page);
    setIncompleteStudentsPageSize(pageSize);
    await handleFetchIncompleteStudents(page, pageSize);
  };

  // 处理未填写学生筛选变化
  const handleIncompleteStudentsFilterChange = async (newFilters: any) => {
    setIncompleteStudentsFilters(newFilters);
    // setIncompleteStudentsPage(1); // 重置到第一页
    await handleFetchIncompleteStudents(
      1,
      incompleteStudentsPageSize,
      newFilters,
    );
  };

  // 处理触发统计
  const handleTriggerStatistics = async (questionnaireId: number) => {
    await triggerStatistics(questionnaireId);
    // 触发后立即获取状态
    await fetchStatisticsTaskStatus(questionnaireId);
  };

  // 处理刷新统计状态
  const handleRefreshStatus = async (questionnaireId: number) => {
    await fetchStatisticsTaskStatus(questionnaireId);
  };

  // 处理导出
  const handleExport = () => {
    setExportModalVisible(true);
  };

  // 处理导出确认
  const handleExportConfirm = async (options: IExportOptions) => {
    const schoolCode = getCurrentUserSchoolCode();
    const currentMonth = filters.month || dayjs().format('YYYY-MM');
    const questionnaireId = filters.questionnaire_id;

    // 构建基础参数
    const baseParams: any = {
      sso_school_code: schoolCode,
    };

    // 只添加有值的可选参数
    if (currentMonth) {
      baseParams.month = currentMonth;
    }

    // export_format 默认为 xlsx，根据文档目前仅支持 xlsx
    if (options.format && options.format === 'xlsx') {
      baseParams.export_format = 'xlsx';
    }

    // 验证参数
    const validation = validateExportParams(baseParams);
    if (!validation.valid) {
      message.error(validation.message);
      return;
    }

    setExportLoading(true);
    try {
      let response;

      // 根据导出类型调用对应的专用接口
      switch (options.type) {
        case ExportType.SCHOOL_STATISTICS:
          const schoolParams: any = {
            export_type: 'school_statistics',  // 必填参数
            sso_school_code: schoolCode,       // 必填参数
          };

          // 根据API文档的可选参数
          if (questionnaireId) {
            schoolParams.questionnaire_id = questionnaireId;
          }
          if (currentMonth) {
            schoolParams.month = currentMonth;
          }
          if (options.format) {
            schoolParams.export_format = options.format;
          }
          if (options.startMonth) {
            schoolParams.start_month = options.startMonth;
          }
          if (options.endMonth) {
            schoolParams.end_month = options.endMonth;
          }
          if (options.includeTrend !== undefined) {
            schoolParams.include_trend = options.includeTrend;
          }
          if (options.includeTeacherRanking !== undefined) {
            schoolParams.include_teacher_ranking = options.includeTeacherRanking;
          }
          if (options.includeIncompleteStudents !== undefined) {
            schoolParams.include_incomplete_students = options.includeIncompleteStudents;
          }

          console.log('学校统计导出参数:', schoolParams);
          response = await exportSchoolStatistics(schoolParams);
          break;

        case ExportType.TEACHER_RANKING:
          const rankingParams: any = {
            export_type: 'teacher_ranking',    // 必填参数
            sso_school_code: schoolCode,       // 必填参数
          };

          // 根据API文档的可选参数
          if (currentMonth) {
            rankingParams.month = currentMonth;
          }
          if (options.subject) {
            rankingParams.subject = options.subject;
          }
          if (options.department) {
            rankingParams.department = options.department;
          }
          if (options.sortBy) {
            rankingParams.sort_by = options.sortBy;
          }
          if (options.sortOrder) {
            rankingParams.sort_order = options.sortOrder;
          }
          if (options.limit) {
            rankingParams.limit = options.limit;
          }
          if (options.format) {
            rankingParams.export_format = options.format;
          }

          console.log('教师排名导出参数:', rankingParams);
          response = await exportTeacherRanking(rankingParams);
          break;

        case ExportType.QUESTIONNAIRE_RESPONSES:
          const responseParams: any = {
            export_type: 'questionnaire_responses',  // 必填参数
            sso_school_code: schoolCode,             // 必填参数
          };

          // 根据API文档的可选参数
          if (questionnaireId) {
            responseParams.questionnaire_id = questionnaireId;
          }
          if (currentMonth) {
            responseParams.month = currentMonth;
          }
          if (options.gradeCode) {
            responseParams.grade_code = options.gradeCode;
          }
          if (options.classCode) {
            responseParams.class_code = options.classCode;
          }
          if (options.includeAnswers !== undefined) {
            responseParams.include_answers = options.includeAnswers;
          }
          if (options.format) {
            responseParams.export_format = options.format;
          }

          console.log('问卷响应导出参数:', responseParams);
          response = await exportQuestionnaireResponses(responseParams);
          break;

        case ExportType.INCOMPLETE_STUDENTS:
          const incompleteParams: any = {
            export_type: 'incomplete_students',  // 必填参数
            sso_school_code: schoolCode,         // 必填参数
          };

          // 根据API文档的可选参数
          if (questionnaireId) {
            incompleteParams.questionnaire_id = questionnaireId;
          }
          if (currentMonth) {
            incompleteParams.month = currentMonth;
          }
          if (options.gradeCode) {
            incompleteParams.grade_code = options.gradeCode;
          }
          if (options.classCode) {
            incompleteParams.class_code = options.classCode;
          }
          if (options.includeContactInfo !== undefined) {
            incompleteParams.include_contact_info = options.includeContactInfo;
          }
          if (options.format) {
            incompleteParams.export_format = options.format;
          }

          console.log('未完成学生导出参数:', incompleteParams);
          response = await exportIncompleteStudents(incompleteParams);
          break;

        case ExportType.COMPREHENSIVE_REPORT:
          const reportParams: any = {
            export_type: 'comprehensive_report',  // 必填参数
            sso_school_code: schoolCode,           // 必填参数
          };

          // 根据API文档的可选参数
          if (currentMonth) {
            reportParams.month = currentMonth;
          }
          if (options.startMonth) {
            reportParams.start_month = options.startMonth;
          }
          if (options.endMonth) {
            reportParams.end_month = options.endMonth;
          }
          if (options.format) {
            reportParams.export_format = options.format;
          }
          if (options.includeSchoolSummary !== undefined) {
            reportParams.include_school_summary = options.includeSchoolSummary;
          }
          if (options.includeTeacherRanking !== undefined) {
            reportParams.include_teacher_ranking = options.includeTeacherRanking;
          }
          if (options.includeGradeAnalysis !== undefined) {
            reportParams.include_grade_analysis = options.includeGradeAnalysis;
          }
          if (options.includeSubjectAnalysis !== undefined) {
            reportParams.include_subject_analysis = options.includeSubjectAnalysis;
          }
          if (options.includeTrendAnalysis !== undefined) {
            reportParams.include_trend_analysis = options.includeTrendAnalysis;
          }
          if (options.includeCompletionAnalysis !== undefined) {
            reportParams.include_completion_analysis = options.includeCompletionAnalysis;
          }

          console.log('综合报表导出参数:', reportParams);
          response = await exportComprehensiveReport(reportParams);
          break;

        default:
          message.error('不支持的导出类型');
          return;
      }

      // 处理导出响应
      handleExportResponse(response);
      setExportModalVisible(false);
      message.success('导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败，请重试');
    } finally {
      setExportLoading(false);
    }
  };

  // 处理导出取消
  const handleExportCancel = () => {
    setExportModalVisible(false);
  };

  // 处理学校数据快速导出
  const handleExportSchoolData = () => {
    const options: IExportOptions = {
      type: ExportType.SCHOOL_STATISTICS,
      format: ExportFormat.XLSX,
      // 暂时不传递可选参数，先测试基本功能
    };

    handleExportConfirm(options);
  };

  // 处理教师排名快速导出
  const handleExportRanking = () => {
    const options: IExportOptions = {
      type: ExportType.TEACHER_RANKING,
      format: ExportFormat.XLSX,
      sortBy: 'average_score',
      sortOrder: 'DESC',
      limit: 100,
    };

    handleExportConfirm(options);
  };

  return (
    <div className="statistics-page">
      {/* 筛选表单 */}
      <FilterForm
        loading={loading}
        taskLoading={taskLoading}
        isPolling={isPolling}
        statisticsTaskStatus={statisticsTaskStatus}
        onFilter={handleFilter}
        onReset={handleReset}
        onTriggerStatistics={handleTriggerStatistics}
        onRefreshStatus={handleRefreshStatus}
        onStopPolling={() => stopPolling()}
        onExport={handleExport}
      />

      {/* 学校整体统计 */}
      <SchoolOverview
        data={schoolStatistics}
        loading={loading}
        statisticsTaskStatus={statisticsTaskStatus}
        onShowIncompleteStudents={() => handleFetchIncompleteStudents(1, 20)}
        onExportSchoolData={handleExportSchoolData}
      />

      {/* 未填写学生统计 */}
      {showIncompleteStudents && (
        <IncompleteStudents
          data={incompleteStudents}
          cachedData={cachedIncompleteStudents}
          loading={incompleteStudentsLoading}
          useCachedData={statisticsTaskStatus?.status === 'completed'}
          onRefresh={handleRefreshIncompleteStudents}
          onPageChange={handleIncompleteStudentsPageChange}
          onFilterChange={handleIncompleteStudentsFilterChange}
        />
      )}

      {/* 教师排行榜 */}
      <TeacherRanking
        data={teacherRanking}
        total={teacherTotal}
        loading={loading}
        currentPage={currentPage}
        pageSize={pageSize}
        onTeacherClick={handleTeacherClick}
        onPageChange={handlePageChange}
        onSortChange={handleSortChange}
        onExportRanking={handleExportRanking}
      />

      {/* 教师详情模态框 */}
      <TeacherDetailModal
        visible={modalVisible}
        loading={modalLoading}
        teacherDetail={teacherDetail}
        scoreDistribution={scoreDistribution}
        keywordData={keywordData}
        onClose={handleModalClose}
      />

      {/* 导出配置模态框 */}
      <ExportModal
        visible={exportModalVisible}
        loading={exportLoading}
        currentMonth={filters.month}
        currentQuestionnaireId={filters.questionnaire_id}
        onCancel={handleExportCancel}
        onExport={handleExportConfirm}
      />
    </div>
  );
};

export default Statistics;
