import type { ITeacherRanking } from '@/types/statistics';
import { DownloadOutlined, TrophyOutlined, UserOutlined } from '@ant-design/icons';
import type { ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { Avatar, Button, Card, Space, Tag, Tooltip } from 'antd';
import React from 'react';

interface TeacherRankingProps {
  data: ITeacherRanking[];
  total: number;
  loading?: boolean;
  onTeacherClick: (teacherId: string) => void;
  onPageChange?: (page: number, pageSize: number) => void;
  onSortChange?: (sortBy: string, sortOrder: 'ASC' | 'DESC') => void;
  onExportRanking?: () => void;
  currentPage?: number;
  pageSize?: number;
}

/**
 * 教师评分排行榜组件
 */
const TeacherRanking: React.FC<TeacherRankingProps> = ({
  data,
  total,
  loading = false,
  onTeacherClick,
  onPageChange,
  onSortChange,
  onExportRanking,
  currentPage = 1,
  pageSize = 20,
}) => {
  // 处理分页变化
  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    // 处理分页
    if (onPageChange && pagination) {
      onPageChange(pagination.current, pagination.pageSize);
    }

    // 处理排序
    if (onSortChange && sorter && sorter.field) {
      const sortBy = sorter.field;
      const sortOrder = sorter.order === 'ascend' ? 'ASC' : 'DESC';
      onSortChange(sortBy, sortOrder);
    }
  };

  // 获取排名图标
  const getRankIcon = (rank: number) => {
    if (rank === 1) return <TrophyOutlined style={{ color: '#faad14' }} />;
    if (rank === 2) return <TrophyOutlined style={{ color: '#d9d9d9' }} />;
    if (rank === 3) return <TrophyOutlined style={{ color: '#cd7f32' }} />;
    return <span style={{ color: '#999' }}>{rank}</span>;
  };

  // 获取评分颜色
  const getScoreColor = (score: number) => {
    if (score >= 90) return '#52c41a';
    if (score >= 80) return '#faad14';
    if (score >= 70) return '#fa8c16';
    return '#f5222d';
  };

  // 表格列定义
  const columns: ProColumns<ITeacherRanking>[] = [
    {
      title: '排名',
      dataIndex: 'rank',
      key: 'rank',
      width: 80,
      align: 'center',
      search: false,
      render: (_, record) => getRankIcon(record.rank),
      hidden: true,
    },
    {
      title: '教师信息',
      key: 'teacher',
      search: false,
      render: (_, record) => (
        <Space>
          <Avatar size="small" icon={<UserOutlined />} />
          <div>
            <div style={{ fontWeight: 500 }}>
              {record.sso_teacher_name || '未知教师'}
            </div>
            <div style={{ fontSize: '12px', color: '#999' }}>
              {record.sso_teacher_department || '未知部门'}
            </div>
          </div>
        </Space>
      ),
    },
    {
      title: '学科',
      dataIndex: 'sso_teacher_subject',
      key: 'subject',
      width: 120,
      search: false,
      render: (_, record) => (
        <Tag color="blue">{record.sso_teacher_subject || '未知学科'}</Tag>
      ),
    },
    {
      title: '平均分',
      dataIndex: 'average_score',
      key: 'average_score',
      width: 100,
      align: 'center',
      search: false,
      sorter: true,
      render: (_, record) => (
        <span
          style={{
            color: getScoreColor(record.average_score),
            fontWeight: 500,
          }}
        >
          {record.average_score?.toFixed(1) || 0}
        </span>
      ),
    },
    {
      title: '评价人数',
      dataIndex: 'evaluation_count',
      key: 'evaluation_count',
      width: 100,
      align: 'center',
      search: false,
      sorter: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      align: 'center',
      search: false,
      render: (_, record) => (
        <Button
          type="link"
          size="small"
          onClick={() => onTeacherClick(record.sso_teacher_id)}
        >
          查看详情
        </Button>
      ),
    },
  ];

  return (
    <Card
      title="教师评分排行榜"
      extra={
        onExportRanking && (
          <Tooltip title="导出教师排名数据">
            <Button
              type="primary"
              icon={<DownloadOutlined />}
              onClick={onExportRanking}
              size="small"
            >
              导出排名
            </Button>
          </Tooltip>
        )
      }
    >
      <ProTable<ITeacherRanking>
        columns={columns}
        dataSource={data}
        loading={loading}
        rowKey="sso_teacher_id"
        search={false}
        options={false}
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          total: total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
        }}
        scroll={{ x: 800 }}
        onChange={handleTableChange}
        onRow={(record) => ({
          style: { cursor: 'pointer' },
          onClick: () => onTeacherClick(record.sso_teacher_id),
        })}
      />
    </Card>
  );
};

export default TeacherRanking;
