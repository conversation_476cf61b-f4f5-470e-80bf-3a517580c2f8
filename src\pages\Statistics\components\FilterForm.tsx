import { getQuestionnaireList } from '@/services/questionnaire';
import type {
  IStatisticsQuery,
  IStatisticsTaskStatusResponse,
} from '@/types/statistics';
import { handleApiResponse } from '@/utils/errorHandler';
import {
  ReloadOutlined,
  SearchOutlined,
  StopOutlined,
  ThunderboltOutlined,
} from '@ant-design/icons';
import {
  Button,
  Card,
  Col,
  Form,
  Row,
  Select,
  Space,
  Tooltip,
  message,
} from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';

const { Option } = Select;

interface FilterFormProps {
  loading?: boolean;
  taskLoading?: boolean;
  isPolling?: boolean;
  statisticsTaskStatus?: IStatisticsTaskStatusResponse | null;
  onFilter: (values: IStatisticsQuery) => void;
  onReset: () => void;
  onTriggerStatistics?: (questionnaireId: number) => void;
  onRefreshStatus?: (questionnaireId: number) => void;
  onStopPolling?: () => void;
}

/**
 * 数据筛选表单组件
 */
const FilterForm: React.FC<FilterFormProps> = ({
  loading = false,
  taskLoading = false,
  isPolling = false,
  statisticsTaskStatus,
  onFilter,
  onReset,
  onTriggerStatistics,
  onStopPolling,
}) => {
  const [form] = Form.useForm();
  const [questionnaireList, setQuestionnaireList] = useState<any[]>([]);
  const [questionnaireLoading, setQuestionnaireLoading] = useState(false);

  // 处理筛选
  const handleFilter = (values: any) => {
    const filterParams: IStatisticsQuery = {
      month: values.month ? dayjs(values.month).format('YYYY-MM') : undefined,
      questionnaire_id: values.questionnaire_id,
    };
    onFilter(filterParams);
  };

  // 根据月份获取问卷列表
  const fetchQuestionnaireListByMonth = async (month?: string) => {
    if (!month) {
      setQuestionnaireList([]);
      return;
    }

    setQuestionnaireLoading(true);
    try {
      const response = await getQuestionnaireList({
        status: 'published', // 只获取已发布的问卷
        month: month, // 根据月份筛选
        page: 1,
        limit: 100,
      });
      const result = handleApiResponse(response);

      if (result.success) {
        const questionnaires = result.data?.list || [];
        setQuestionnaireList(questionnaires);

        // 如果有问卷，自动选择第一个；如果没有，清空选择
        if (questionnaires.length > 0) {
          form.setFieldsValue({
            questionnaire_id: questionnaires[0].id,
          });
        } else {
          form.setFieldsValue({
            questionnaire_id: undefined,
          });
          message.info(`${month} 月暂无可用问卷`);
        }
      } else {
        setQuestionnaireList([]);
        form.setFieldsValue({
          questionnaire_id: undefined,
        });
        message.error('获取问卷列表失败');
      }
    } catch (error) {
      setQuestionnaireList([]);
      form.setFieldsValue({
        questionnaire_id: undefined,
      });
      message.error('获取问卷列表失败');
    } finally {
      setQuestionnaireLoading(false);
    }
  };

  // 处理月份变化
  const handleMonthChange = (month: string) => {
    // 月份变化时，重新获取该月份的问卷列表
    fetchQuestionnaireListByMonth(month);
  };

  // 处理触发统计
  const handleTriggerStatistics = () => {
    const questionnaireId = form.getFieldValue('questionnaire_id');
    const month = form.getFieldValue('month');

    if (!month) {
      message.warning('请先选择月份');
      return;
    }

    if (!questionnaireId) {
      message.warning('当前月份暂无可用问卷，无法触发统计分析');
      return;
    }

    if (onTriggerStatistics) {
      onTriggerStatistics(questionnaireId);
    } else {
      console.warn('onTriggerStatistics 回调函数未定义');
    }
  };

  // 获取状态显示文本
  const getStatusText = () => {
    if (!statisticsTaskStatus) return '';

    let statusText = '';
    switch (statisticsTaskStatus.status) {
      case 'pending':
        statusText = '等待计算';
        break;
      case 'calculating':
        statusText = '计算中...';
        break;
      case 'completed':
        statusText = `计算完成 (${
          statisticsTaskStatus.last_calculated_at
            ? dayjs(statisticsTaskStatus.last_calculated_at).format(
                'MM-DD HH:mm',
              )
            : ''
        })`;
        break;
      case 'failed':
        statusText = '计算失败';
        break;
      default:
        statusText = '';
    }

    // 如果正在轮询，添加轮询标识
    if (
      isPolling &&
      (statisticsTaskStatus.status === 'calculating' ||
        statisticsTaskStatus.status === 'pending')
    ) {
      statusText += ' (自动刷新中)';
    }

    return statusText;
  };

  // 处理停止轮询
  const handleStopPolling = () => {
    if (onStopPolling) {
      onStopPolling();
    }
  };

  // 处理重置
  const handleReset = () => {
    form.resetFields();
    onReset();
  };

  // 生成月份选项（最近12个月）
  const getMonthOptions = () => {
    const months = [];
    const current = dayjs();

    for (let i = 0; i < 12; i++) {
      const month = current.subtract(i, 'month');
      months.push({
        value: month.format('YYYY-MM'),
        label: month.format('YYYY年MM月'),
      });
    }

    return months;
  };

  // 初始化数据
  useEffect(() => {
    const currentMonth = dayjs().format('YYYY-MM');

    // 设置默认月份
    form.setFieldsValue({
      month: currentMonth,
    });

    // 根据默认月份获取问卷列表
    fetchQuestionnaireListByMonth(currentMonth);
  }, [form]);

  return (
    <Card
      className="filter-form-card"
      style={{
        marginBottom: 16,
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        border: 'none',
        color: 'white',
      }}
      styles={{ body: { padding: '20px 24px' } }}
    >
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: 16,
        }}
      >
        <div
          style={{
            fontSize: '18px',
            fontWeight: 600,
            color: 'white',
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <SearchOutlined style={{ marginRight: 8, fontSize: '20px' }} />
          数据筛选
        </div>
        {statisticsTaskStatus && (
          <div
            style={{
              background: 'rgba(255,255,255,0.2)',
              padding: '4px 12px',
              borderRadius: '12px',
              fontSize: '12px',
              color: 'white',
              fontWeight: 500,
            }}
          >
            状态：{getStatusText()}
          </div>
        )}
      </div>

      <Form
        form={form}
        layout="inline"
        onFinish={handleFilter}
        style={{ width: '100%' }}
      >
        <Row gutter={16} style={{ width: '100%' }}>
          <Col span={6}>
            <Form.Item
              name="questionnaire_id"
              label={
                <span style={{ color: 'white', fontWeight: 500 }}>问卷</span>
              }
            >
              <Select
                placeholder={
                  !form.getFieldValue('month')
                    ? '请先选择月份'
                    : questionnaireList.length === 0
                    ? '该月份暂无问卷'
                    : '请选择问卷'
                }
                style={{ width: '100%' }}
                size="large"
                loading={questionnaireLoading}
                disabled={
                  !form.getFieldValue('month') || questionnaireList.length === 0
                }
                notFoundContent={
                  questionnaireLoading
                    ? '加载中...'
                    : !form.getFieldValue('month')
                    ? '请先选择月份'
                    : '该月份暂无问卷'
                }
              >
                {questionnaireList.map((questionnaire) => (
                  <Option key={questionnaire.id} value={questionnaire.id}>
                    {questionnaire.title}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>

          <Col span={6}>
            <Form.Item
              name="month"
              label={
                <span style={{ color: 'white', fontWeight: 500 }}>月份</span>
              }
            >
              <Select
                placeholder="请选择月份"
                allowClear
                style={{ width: '100%' }}
                size="large"
                onChange={handleMonthChange}
              >
                {getMonthOptions().map((month) => (
                  <Option key={month.value} value={month.value}>
                    {month.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item>
              <Space wrap>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<SearchOutlined />}
                  loading={loading}
                  size="large"
                  style={{
                    background: 'rgba(255,255,255,0.2)',
                    borderColor: 'rgba(255,255,255,0.3)',
                    color: 'white',
                    fontWeight: 500,
                  }}
                >
                  查询
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={handleReset}
                  size="large"
                  style={{
                    background: 'transparent',
                    borderColor: 'rgba(255,255,255,0.3)',
                    color: 'white',
                  }}
                >
                  重置
                </Button>
                <Tooltip
                  title={
                    !form.getFieldValue('month')
                      ? '请先选择月份'
                      : !form.getFieldValue('questionnaire_id')
                      ? '该月份暂无可用问卷，无法触发统计'
                      : '手动触发统计计算，系统将异步执行统计任务'
                  }
                >
                  <Button
                    type="dashed"
                    icon={<ThunderboltOutlined />}
                    loading={taskLoading}
                    onClick={handleTriggerStatistics}
                    size="large"
                    disabled={
                      !form.getFieldValue('month') ||
                      !form.getFieldValue('questionnaire_id')
                    }
                    style={{
                      background: 'rgba(255,255,255,0.1)',
                      borderColor: 'rgba(255,255,255,0.3)',
                      color: 'white',
                      opacity:
                        !form.getFieldValue('month') ||
                        !form.getFieldValue('questionnaire_id')
                          ? 0.5
                          : 1,
                    }}
                  >
                    触发统计
                  </Button>
                </Tooltip>
                {isPolling && (
                  <Tooltip title="停止自动刷新统计状态">
                    <Button
                      type="default"
                      icon={<StopOutlined />}
                      onClick={handleStopPolling}
                      size="small"
                      style={{
                        background: 'rgba(255,255,255,0.1)',
                        borderColor: 'rgba(255,255,255,0.3)',
                        color: 'white',
                      }}
                    >
                      停止轮询
                    </Button>
                  </Tooltip>
                )}
              </Space>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Card>
  );
};

export default FilterForm;
