# 统计分析导出功能部署指南

## 🚀 部署准备

### 前端部署
导出功能已完全集成到现有的统计分析模块中，无需额外的部署步骤。

### 后端API要求
确保后端已实现以下导出API接口：

```
POST /api/export/school-statistics
POST /api/export/teacher-statistics  
POST /api/export/teacher-ranking
POST /api/export/questionnaire-responses
POST /api/export/comprehensive-report
POST /api/export/incomplete-students
POST /api/export/general
```

## 📋 API接口规范

### 1. 导出学校统计数据
```http
POST /api/export/school-statistics
Content-Type: application/json

{
  "sso_school_code": "SCHOOL001",
  "questionnaire_id": 123,
  "month": "2024-03",
  "start_month": "2024-01",
  "end_month": "2024-03",
  "export_format": "xlsx",
  "include_trend": true,
  "include_teacher_ranking": true,
  "include_incomplete_students": true
}
```

### 2. 导出教师统计数据
```http
POST /api/export/teacher-statistics
Content-Type: application/json

{
  "sso_school_code": "SCHOOL001",
  "sso_teacher_id": "TEACHER001",
  "month": "2024-03",
  "start_month": "2024-01",
  "end_month": "2024-03",
  "export_format": "xlsx",
  "include_distribution": true,
  "include_keywords": true,
  "include_trend": true
}
```

### 3. 导出教师排名数据
```http
POST /api/export/teacher-ranking
Content-Type: application/json

{
  "sso_school_code": "SCHOOL001",
  "month": "2024-03",
  "subject": "数学",
  "department": "初中部",
  "sort_by": "average_score",
  "sort_order": "DESC",
  "limit": 100,
  "export_format": "xlsx"
}
```

### 4. 导出问卷响应数据
```http
POST /api/export/questionnaire-responses
Content-Type: application/json

{
  "sso_school_code": "SCHOOL001",
  "questionnaire_id": 1,
  "month": "2024-03",
  "grade_code": "7",
  "class_code": "1",
  "is_completed": true,
  "include_answers": true,
  "export_format": "xlsx"
}
```

### 5. 导出综合报表
```http
POST /api/export/comprehensive-report
Content-Type: application/json

{
  "sso_school_code": "SCHOOL001",
  "month": "2024-03",
  "start_month": "2024-01",
  "end_month": "2024-03",
  "export_format": "xlsx",
  "include_school_summary": true,
  "include_teacher_ranking": true,
  "include_grade_analysis": true,
  "include_subject_analysis": true,
  "include_trend_analysis": true,
  "include_completion_analysis": true
}
```

### 6. 导出未完成学生名单
```http
POST /api/export/incomplete-students
Content-Type: application/json

{
  "sso_school_code": "SCHOOL001",
  "questionnaire_id": 1,
  "month": "2024-03",
  "grade_code": "7",
  "class_code": "1",
  "include_contact_info": true,
  "export_format": "xlsx"
}
```

### 7. 通用导出接口
```http
POST /api/export/general
Content-Type: application/json

{
  "export_type": "school_statistics",
  "sso_school_code": "SCHOOL001",
  "month": "2024-03",
  "export_format": "xlsx"
}
```

## 📤 响应格式要求

### 成功响应
- **Content-Type**: `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet` (Excel) 或 `text/csv` (CSV)
- **Content-Disposition**: `attachment; filename="文件名.xlsx"`
- **响应体**: 文件二进制数据

### 错误响应
```json
{
  "errCode": 1,
  "msg": "错误信息",
  "data": null
}
```

## 🔧 配置说明

### 前端配置
无需额外配置，导出功能已集成到现有的统计分析页面中。

### 后端配置建议
1. **文件大小限制**: 建议设置合理的导出文件大小限制
2. **超时设置**: 大数据量导出可能需要较长时间，建议设置适当的超时时间
3. **并发控制**: 建议限制同时进行的导出任务数量
4. **缓存策略**: 建议使用缓存数据提升导出性能

## 🧪 测试验证

### 功能测试清单
- [ ] 学校统计数据导出
- [ ] 教师排名数据导出  
- [ ] 问卷响应数据导出
- [ ] 未完成学生名单导出
- [ ] 综合报表导出
- [ ] 快速导出按钮功能
- [ ] 导出配置模态框
- [ ] 文件下载功能
- [ ] 错误处理机制

### 测试用例
1. **正常导出流程**
   - 选择筛选条件
   - 点击导出按钮
   - 配置导出选项
   - 验证文件下载

2. **边界条件测试**
   - 无数据时的导出
   - 大数据量导出
   - 网络异常处理

3. **权限验证**
   - 验证用户只能导出自己学校的数据
   - 验证必要参数的校验

## 🔍 故障排查

### 常见问题

1. **导出按钮不显示**
   - 检查用户权限
   - 确认统计数据已加载

2. **导出失败**
   - 检查API接口是否正常
   - 验证请求参数格式
   - 查看浏览器控制台错误信息

3. **文件下载失败**
   - 检查浏览器下载设置
   - 验证响应头Content-Disposition
   - 确认文件MIME类型正确

### 调试方法
1. 打开浏览器开发者工具
2. 查看Network标签页的API请求
3. 检查Console标签页的错误信息
4. 验证响应数据格式

## 📞 技术支持

如遇到问题，请提供以下信息：
1. 浏览器类型和版本
2. 操作步骤描述
3. 错误信息截图
4. 浏览器控制台日志

## 🎯 性能优化建议

1. **后端优化**
   - 使用缓存数据提升导出速度
   - 实现异步导出处理
   - 添加导出进度反馈

2. **前端优化**
   - 添加导出进度显示
   - 实现导出任务队列
   - 优化大文件下载体验

## 📈 监控指标

建议监控以下指标：
- 导出请求成功率
- 导出响应时间
- 导出文件大小分布
- 用户导出行为统计
