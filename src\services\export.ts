// @ts-ignore
/* eslint-disable */
import type {
  ISchoolStatisticsExportParams,
  ITeacherStatisticsExportParams,
  ITeacherRankingExportParams,
  IQuestionnaireResponsesExportParams,
  IComprehensiveReportExportParams,
  IIncompleteStudentsExportParams,
  IGeneralExportParams,
} from '@/types/export';
import { request } from '@umijs/max';

/**
 * 导出服务
 * @description 统计数据导出相关操作
 */

/**
 * 导出学校统计数据
 * POST /api/export/school-statistics
 */
export async function exportSchoolStatistics(params: any) {
  return request('/api/export/school-statistics', {
    method: 'POST',
    data: params,
    responseType: 'blob',
    getResponse: true,
  });
}

/**
 * 导出教师统计数据
 * POST /api/export/teacher-statistics
 */
export async function exportTeacherStatistics(params: any) {
  return request('/api/export/teacher-statistics', {
    method: 'POST',
    data: params,
    responseType: 'blob',
    getResponse: true,
  });
}

/**
 * 导出教师排名数据
 * POST /api/export/teacher-ranking
 */
export async function exportTeacherRanking(params: any) {
  return request('/api/export/teacher-ranking', {
    method: 'POST',
    data: params,
    responseType: 'blob',
    getResponse: true,
  });
}

/**
 * 导出问卷响应数据
 * POST /api/export/questionnaire-responses
 */
export async function exportQuestionnaireResponses(params: any) {
  return request('/api/export/questionnaire-responses', {
    method: 'POST',
    data: params,
    responseType: 'blob',
    getResponse: true,
  });
}

/**
 * 导出综合报表
 * POST /api/export/comprehensive-report
 */
export async function exportComprehensiveReport(params: any) {
  return request('/api/export/comprehensive-report', {
    method: 'POST',
    data: params,
    responseType: 'blob',
    getResponse: true,
  });
}

/**
 * 导出未完成学生名单
 * POST /api/export/incomplete-students
 */
export async function exportIncompleteStudents(params: any) {
  return request('/api/export/incomplete-students', {
    method: 'POST',
    data: params,
    responseType: 'blob',
    getResponse: true,
  });
}


