# 更新后的导出功能指南

## 🎯 重要更新

根据您提供的最新API文档，我已经重新调整了导出功能：

### ✅ 主要变更

1. **使用专用导出接口**
   - 不再使用通用接口 `/api/export/general`
   - 每种导出类型使用对应的专用接口

2. **API接口映射**
   ```javascript
   学校统计 → POST /api/export/school-statistics
   教师排名 → POST /api/export/teacher-ranking
   问卷响应 → POST /api/export/questionnaire-responses
   综合报表 → POST /api/export/comprehensive-report
   未完成学生 → POST /api/export/incomplete-students
   ```

3. **参数优化**
   - 移除了 `export_type` 参数（专用接口不需要）
   - 保留所有可选参数的条件传递
   - 添加详细的调试日志

## 🧪 测试步骤

### 1. 快速导出测试（推荐）

**学校统计数据导出**：
1. 进入统计分析页面
2. 选择月份和问卷
3. 点击统计状态卡片右上角的"导出数据"按钮
4. 查看控制台输出：
   ```javascript
   学校统计导出参数: {
     sso_school_code: "SCHOOL001",
     month: "2024-03",
     export_format: "xlsx",
     questionnaire_id: 123  // 如果有选择问卷
   }
   ```
5. 验证请求发送到 `/api/export/school-statistics`

**教师排名导出**：
1. 点击教师排行榜卡片右上角的"导出排名"按钮
2. 查看控制台输出：
   ```javascript
   教师排名导出参数: {
     sso_school_code: "SCHOOL001",
     month: "2024-03", 
     export_format: "xlsx",
     sort_by: "average_score",
     sort_order: "DESC",
     limit: 100
   }
   ```
3. 验证请求发送到 `/api/export/teacher-ranking`

### 2. 配置导出测试

1. **点击筛选表单右侧的"导出"按钮**
2. **选择导出类型**（学校统计、教师排名、问卷响应等）
3. **配置导出选项**（可选）
4. **点击"开始导出"**
5. **验证对应的API接口被调用**

## 🔍 调试信息

### 控制台日志
我已经为每种导出类型添加了专门的调试日志：
- `学校统计导出参数:`
- `教师排名导出参数:`
- `问卷响应导出参数:`
- `未完成学生导出参数:`
- `综合报表导出参数:`

### 网络请求验证
在开发者工具 → Network 中验证：
- **URL**: 对应的专用导出接口
- **Method**: POST
- **Content-Type**: application/json
- **请求体**: 包含正确的参数

## 📋 支持的导出类型

### 1. 学校统计数据
- **接口**: `/api/export/school-statistics`
- **必需参数**: `sso_school_code`
- **推荐参数**: `questionnaire_id`（使用缓存数据，速度更快）
- **可选参数**: `month`, `include_trend`, `include_teacher_ranking`, `include_incomplete_students`

### 2. 教师排名数据
- **接口**: `/api/export/teacher-ranking`
- **必需参数**: `sso_school_code`
- **默认参数**: `sort_by: "average_score"`, `sort_order: "DESC"`, `limit: 100`
- **可选参数**: `month`, `subject`, `department`

### 3. 问卷响应数据
- **接口**: `/api/export/questionnaire-responses`
- **必需参数**: `sso_school_code`
- **推荐参数**: `questionnaire_id`
- **可选参数**: `month`, `grade_code`, `class_code`, `include_answers`

### 4. 未完成学生名单
- **接口**: `/api/export/incomplete-students`
- **必需参数**: `sso_school_code`
- **推荐参数**: `questionnaire_id`
- **可选参数**: `month`, `grade_code`, `include_contact_info`

### 5. 综合报表
- **接口**: `/api/export/comprehensive-report`
- **必需参数**: `sso_school_code`
- **可选参数**: `month`, 各种 `include_*` 选项

## 🎯 预期结果

### ✅ 成功标志
- 控制台显示正确的导出参数
- 网络请求发送到正确的专用接口
- 不再出现参数相关错误
- 文件成功下载

### 📊 性能优化
- **学校统计导出**: 提供 `questionnaire_id` 参数使用缓存数据
- **教师排名导出**: 设置合理的 `limit` 参数
- **大数据导出**: 建议使用缓存数据避免超时

## 🔧 故障排查

### 常见问题
1. **参数错误**: 检查控制台输出的参数格式
2. **接口错误**: 验证网络请求的URL是否正确
3. **权限错误**: 确认用户有导出权限
4. **超时错误**: 尝试使用缓存数据或减少导出数量

### 调试技巧
1. **查看控制台日志**: 确认参数正确
2. **检查网络请求**: 验证接口调用
3. **测试快速导出**: 先测试最简单的功能
4. **逐步增加复杂度**: 从基本导出到配置导出

## 🚀 部署建议

1. **部署更新后的代码**
2. **首先测试快速导出功能**
3. **验证各种导出类型**
4. **测试配置选项功能**
5. **检查性能表现**

## 📞 技术支持

如果遇到问题，请提供：
1. **控制台日志**: 导出参数的完整输出
2. **网络请求**: 开发者工具中的请求详情
3. **错误信息**: 完整的错误消息
4. **操作步骤**: 重现问题的具体步骤

## 🎉 功能特点

- ✅ 使用专用API接口，性能更好
- ✅ 支持缓存数据导出，速度更快
- ✅ 详细的调试日志，便于排查问题
- ✅ 灵活的参数配置，满足不同需求
- ✅ 完善的错误处理，用户体验良好
