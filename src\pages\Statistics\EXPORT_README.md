# 统计分析导出功能

## 功能概述

统计分析模块新增了完整的数据导出功能，支持多种导出类型和格式，满足不同场景的数据导出需求。

## 导出功能特性

### 🎯 支持的导出类型

1. **学校统计数据** - 学校整体统计信息、完成率、平均分等
2. **教师排名数据** - 教师评分排行榜、排名信息等  
3. **问卷响应数据** - 问卷填写详情、评价内容等
4. **未完成学生名单** - 未填写问卷的学生信息
5. **综合报表** - 完整的统计分析报告

### 📊 支持的导出格式

- **Excel (.xlsx)** - 推荐格式，支持多工作表
- **CSV (.csv)** - 通用格式，便于数据处理

### ⚡ 导出方式

#### 1. 通用导出按钮
- 位置：筛选表单右侧
- 功能：打开导出配置模态框，支持所有导出类型
- 特点：可自定义导出选项和参数

#### 2. 快速导出按钮
- **学校数据导出**：统计状态卡片右上角
- **教师排名导出**：教师排行榜卡片右上角
- 特点：一键导出，使用默认配置

## 导出配置选项

### 学校统计数据导出
- ✅ 趋势分析
- ✅ 教师排名
- ✅ 未完成学生统计
- 📅 时间范围选择

### 教师排名数据导出
- 🔍 学科筛选
- 🏢 部门筛选
- 📊 排序字段（平均分/评价人数/教师姓名）
- ⬆️⬇️ 排序方向（升序/降序）
- 🔢 导出数量限制

### 问卷响应数据导出
- 🎓 年级筛选
- 🏫 班级筛选
- ✅ 包含答案详情

### 未完成学生名单导出
- 🎓 年级筛选
- 🏫 班级筛选
- ✅ 包含联系信息

### 综合报表导出
- ✅ 学校概览
- ✅ 教师排名
- ✅ 年级分析
- ✅ 学科分析
- ✅ 趋势分析
- ✅ 完成情况分析

## 技术实现

### 核心文件结构

```
src/
├── types/export.ts                    # 导出类型定义
├── services/export.ts                 # 导出API服务
├── utils/export.ts                    # 导出工具函数
└── pages/Statistics/
    ├── components/ExportModal.tsx     # 导出配置模态框
    ├── components/FilterForm.tsx      # 筛选表单（含导出按钮）
    ├── components/SchoolOverview.tsx  # 学校概览（含快速导出）
    ├── components/TeacherRanking.tsx  # 教师排名（含快速导出）
    └── index.tsx                      # 主页面（集成导出功能）
```

### API 接口

#### 1. 导出学校统计数据
```
POST /api/export/school-statistics
```

#### 2. 导出教师统计数据
```
POST /api/export/teacher-statistics
```

#### 3. 导出教师排名数据
```
POST /api/export/teacher-ranking
```

#### 4. 导出问卷响应数据
```
POST /api/export/questionnaire-responses
```

#### 5. 导出综合报表
```
POST /api/export/comprehensive-report
```

#### 6. 导出未完成学生名单
```
POST /api/export/incomplete-students
```

#### 7. 通用导出接口
```
POST /api/export/general
```

### 性能优化

- **缓存优先**：优先使用预计算的缓存统计数据
- **异步处理**：导出过程不阻塞UI操作
- **错误处理**：完善的错误提示和重试机制
- **文件下载**：自动处理文件名和下载流程

## 使用说明

### 1. 通用导出流程

1. 在统计分析页面选择筛选条件（月份、问卷等）
2. 点击筛选表单右侧的"导出"按钮
3. 在弹出的导出配置模态框中：
   - 选择导出类型
   - 选择导出格式
   - 配置导出选项
4. 点击"开始导出"按钮
5. 等待导出完成，文件将自动下载

### 2. 快速导出流程

1. 在统计分析页面选择筛选条件
2. 点击对应卡片右上角的快速导出按钮：
   - 学校数据：点击统计状态卡片的"导出数据"
   - 教师排名：点击教师排行榜的"导出排名"
3. 系统将使用默认配置自动导出并下载文件

### 3. 导出文件说明

- **文件命名**：自动生成包含类型、月份、时间戳的文件名
- **文件格式**：支持Excel多工作表和CSV格式
- **文件内容**：根据选择的导出选项生成对应的数据表

## 注意事项

1. **权限控制**：用户只能导出自己学校的数据
2. **数据实时性**：导出数据基于当前筛选条件和统计状态
3. **文件大小**：大数据量导出可能需要较长时间，请耐心等待
4. **浏览器兼容**：建议使用现代浏览器以获得最佳体验

## 后续优化计划

- [ ] 添加导出进度显示
- [ ] 支持导出任务队列
- [ ] 增加导出历史记录
- [ ] 支持定时导出功能
- [ ] 添加导出数据预览
